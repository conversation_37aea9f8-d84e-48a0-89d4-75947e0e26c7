import React from 'react';
import {Box, Text} from 'ink';
import Button from '../components/Button.js';

type Props = {
	/** 导航函数 */
	onNavigate: (route: string) => void;
};

/**
 * 帮助页面组件
 * 显示应用使用说明和快捷键
 */
export default function HelpPage({onNavigate}: Props) {
	return (
		<Box flexDirection="column" padding={1}>
			<Text color="yellow" bold>
				❓ 帮助页面
			</Text>

			<Box marginTop={1} marginBottom={1} flexDirection="column">
				<Text>使用说明:</Text>
				<Text>• 使用方向键或 Tab 键导航</Text>
				<Text>• 按 Enter 或空格键选择</Text>
				<Text>• 按 Ctrl+C 强制退出</Text>
			</Box>

			<Box flexDirection="column" gap={1}>
				<Button onPress={() => onNavigate('home')}>🏠 返回主页</Button>

				<Button onPress={() => onNavigate('settings')}>⚙️ 设置页面</Button>

				<Button onPress={() => process.exit(0)}>🚪 退出应用</Button>
			</Box>
		</Box>
	);
}
