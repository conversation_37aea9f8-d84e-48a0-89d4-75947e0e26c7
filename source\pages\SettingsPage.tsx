import React from 'react';
import {Box, Text} from 'ink';
import {useInput} from 'ink';
import Button from '../components/Button.js';

type Props = {
	/** 导航函数 */
	onNavigate: (route: string) => void;
};

/**
 * 设置页面组件
 * 显示应用设置选项
 */
export default function SettingsPage({onNavigate}: Props) {
	return (
		<Box flexDirection="column" padding={1}>
			<Text color="cyan" bold>
				⚙️ 设置页面
			</Text>

			<Box marginTop={1} marginBottom={1}>
				<Text>欢迎来到设置页面！</Text>
			</Box>

			<Box flexDirection="column" gap={1}>
				<Button onPress={() => onNavigate('home')}>🏠 返回主页</Button>

				<Button onPress={() => onNavigate('help')}>❓ 帮助页面</Button>

				<Button onPress={() => process.exit(0)}>🚪 退出应用</Button>
			</Box>
		</Box>
	);
}
