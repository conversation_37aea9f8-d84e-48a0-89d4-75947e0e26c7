import React from 'react';
import { Text, Box } from 'ink';
import { useInput } from 'ink';

type Props = {
	/** 按钮显示内容 */
	children: React.ReactNode;
	/** 点击回调函数 */
	onPress: () => void;
	/** 是否禁用 */
	disabled?: boolean;
};

/**
 * 可交互按钮组件
 * 支持键盘导航和点击事件
 */
export default function Button({ children, onPress, disabled = false }: Props) {
	// 监听键盘输入
	useInput((input, key) => {
		if (!disabled && (key.return || input === ' ')) {
			onPress();
		}
	});

	return (
		<Box
			borderStyle="single"
			paddingX={2}
			paddingY={1}
			borderColor={disabled ? 'gray' : 'cyan'}
		>
			<Text color={disabled ? 'gray' : 'white'}>
				{children}
			</Text>
		</Box>
	);
}
