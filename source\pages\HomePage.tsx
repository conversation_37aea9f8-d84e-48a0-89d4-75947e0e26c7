import React from 'react';
import { Box, Text } from 'ink';
import Button from '../components/Button.js';

type Props = {
	/** 用户名称 */
	name: string;
	/** 导航函数 */
	onNavigate: (route: string) => void;
};

/**
 * 主页组件
 * 显示欢迎信息和主要功能入口
 */
export default function HomePage({ name, onNavigate }: Props) {
	return (
		<Box flexDirection="column" padding={1}>
			<Text color="green" bold>
				🏠 主页
			</Text>

			<Box marginTop={1} marginBottom={1}>
				<Text>
					你好, <Text color="cyan" bold>{name}</Text>!
					欢迎使用 CLI 应用。
				</Text>
			</Box>

			<Box flexDirection="column" marginTop={2}>
				<Text color="yellow" bold>可用操作:</Text>

				<Box marginTop={1} flexDirection="column">
					<Button onPress={() => onNavigate('settings')}>
						⚙️  设置页面
					</Button>

					<Button onPress={() => onNavigate('help')}>
						❓ 帮助页面
					</Button>

					<Button onPress={() => process.exit(0)}>
						🚪 退出应用
					</Button>
				</Box>
			</Box>
		</Box>
	);
}
